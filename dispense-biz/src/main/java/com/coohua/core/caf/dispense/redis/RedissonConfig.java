package com.coohua.core.caf.dispense.redis;

import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.stream.Collectors;

@Configuration
@Slf4j
public class RedissonConfig {

    @Value("${spring.redis2.cluster.nodes}")
    private String redis2Nodes;

    @Value("${spring.redis2.timeout:2000}")
    private long redis2Timeout;

    @Value("${spring.redis2.lettuce.pool.max-active:100}")
    private int redis2MaxActive;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();

        // 配置集群模式
        String[] nodes = redis2Nodes.split(",");
        String[] redisAddresses = new String[nodes.length];

        for (int i = 0; i < nodes.length; i++) {
            redisAddresses[i] = "redis://" + nodes[i];
        }

        config.useClusterServers()
                .addNodeAddress(redisAddresses)
                .setConnectTimeout((int) redis2Timeout)
                .setTimeout((int) redis2Timeout)
                .setMasterConnectionPoolSize(redis2MaxActive)
                .setSlaveConnectionPoolSize(redis2MaxActive)
                .setRetryAttempts(3)
                .setRetryInterval(1500)
                .setScanInterval(2000);

        // 性能优化配置
        config.setThreads(16)
                .setNettyThreads(32)
                .setKeepPubSubOrder(false)
        ;

        RedissonClient redissonClient = Redisson.create(config);
        log.info("Redisson客户端已创建，集群节点: {}", redis2Nodes);

        return redissonClient;
    }
}
